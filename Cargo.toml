[package]
name = "markov"
version = "0.1.0"
edition = "2024"

[dependencies]
num-bigint = "0.4"
num-rational = "0.4"
num-traits = "0.2"
ndarray = "0.16"
itertools = "0.14"
num-integer = "0.1"
symbolica = { version = "0.17", optional = true }

[features]
symbolica_backend = ["symbolica"]
measure_time = []
default = ["measure_time"]

[profile.release]
strip = true
codegen-units = 1
lto = true
panic = "abort"
# debug = true