use num_bigint::BigInt;
use num_rational::BigRational;

use crate::greybough::GreyboughState;

mod greybough;
mod state_calc;

fn main() {
    let mut cache = std::collections::BTreeMap::new();
    let v = vec![2, 2, 2, 2, 2, 2, 2];
    let now = std::time::Instant::now();
    let opt = GreyboughState::solve(&v, 7, true, &mut cache);
    let opt_time = now.elapsed();
    let now = std::time::Instant::now();
    let avg = GreyboughState::solve(&v, 7, false, &mut cache);
    let avg_time = now.elapsed();
    println!(
        "CACHED {:?} -> OPT {:?} (took {:?}), AVG {:?} (took {:?})",
        v, opt, opt_time, avg, avg_time
    );
}

fn br(i: i64) -> BigRational {
    BigRational::from_integer(BigInt::from(i))
}

fn bf(i: i64, j: i64) -> BigRational {
    BigRational::from_integer(BigInt::from(i)) / BigRational::from_integer(BigInt::from(j))
}

#[test]
fn test_perf() {
    let mut cache = std::collections::BTreeMap::new();
    const N: usize = 16;
    for i in 1..N {
        let mut v = vec![];
        for _ in 0..i {
            v.push(1);
        }
        let _ = GreyboughState::solve(&v, i, true, &mut cache);
    }

    let now = std::time::Instant::now();
    let opt = GreyboughState::solve(&vec![1; N], N, true, &mut cache);
    let opt_time = now.elapsed();
    assert!(
        opt_time.as_secs_f32() < 0.5,
        "Took too long: {:?}",
        opt_time
    );
}

#[test]
fn test_1_opt() {
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![1], 10, true),
        br(1)
    );
}

#[test]
fn test_1_avg() {
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![1], 10, false),
        br(1)
    );
}

#[test]
fn test_2_opt() {
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![2], 10, true),
        br(2)
    );
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![1, 1], 10, true),
        br(3)
    );
}

#[test]
fn test_2_avg() {
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![2], 10, false),
        br(2)
    );
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![1, 1], 10, false),
        br(3)
    );
}

#[test]
fn test_3_opt() {
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![3], 10, true),
        br(4)
    );
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![1, 2], 10, true),
        br(5)
    );
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![1, 1, 1], 10, true),
        br(6)
    );
}

#[test]
fn test_3_avg() {
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![3], 10, false),
        br(4)
    );
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![1, 2], 10, false),
        br(6)
    );
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![1, 1, 1], 10, false),
        br(7)
    );
}

#[test]
fn test_4_opt() {
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![4], 10, true),
        br(7)
    );
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![1, 3], 10, true),
        br(8)
    );
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![2, 2], 10, true),
        br(9)
    );
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![1, 1, 2], 10, true),
        bf(19, 2)
    );
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![1, 1, 1, 1], 10, true),
        bf(21, 2)
    );
}

#[test]
fn test_4_avg() {
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![4], 10, false),
        br(8)
    );
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![1, 3], 10, false),
        br(12)
    );
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![2, 2], 10, false),
        br(13)
    );
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![1, 1, 2], 10, false),
        br(14)
    );
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![1, 1, 1, 1], 10, false),
        br(15)
    );
}

#[test]
fn test_5_opt() {
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![5], 10, true),
        bf(23, 2)
    );
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![1, 4], 10, true),
        bf(25, 2)
    );
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![2, 3], 10, true),
        bf(27, 2)
    );
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![1, 1, 3], 10, true),
        br(14)
    );
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![1, 2, 2], 10, true),
        bf(29, 2)
    );
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![1, 1, 1, 2], 10, true),
        bf(46, 3)
    );
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![1, 1, 1, 1, 1], 10, true),
        bf(49, 3)
    );
}

#[test]
fn test_others() {
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![7], 4, true),
        bf(23, 2)
    );
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![1, 1, 1, 1, 1, 1, 1], 10, true),
        bf(16271, 480)
    );
    assert_eq!(
        GreyboughState::solve_without_cache(&vec![2, 2, 2, 2, 2, 2, 2], 7, true),
        bf(2071956844759, 35831808000)
    );
}

#[test]
fn orig_main() {
    let mut cache = std::collections::BTreeMap::new();
    for i in 1..=20 {
        let mut v = vec![];
        for _ in 0..i {
            v.push(1);
        }
        //v.push(i as u8);
        let now = std::time::Instant::now();
        let opt = GreyboughState::solve(&v, i, true, &mut cache);
        let opt_time = now.elapsed();
        let now = std::time::Instant::now();
        let avg = GreyboughState::solve(&v, i, false, &mut cache);
        let avg_time = now.elapsed();
        println!(
            "CACHED {:?} -> OPT {:?} (took {:?}), AVG {:?} (took {:?})",
            i, opt, opt_time, avg, avg_time
        );
    }
}
