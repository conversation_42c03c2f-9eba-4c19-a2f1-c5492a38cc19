use num_bigint::BigInt;
use num_rational::BigRational;
use num_traits::ToPrimitive;
use std::collections::BTreeMap;

use crate::state_calc::{SolvableStateTransfer, StateTransfer};

const BOARD_SIZE: usize = 7;

#[derive(PartialEq, <PERSON>q, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialOrd, Ord)]
pub struct CthunPatronState {
    targets: [i8; BOARD_SIZE],
    hero: i8,
    remaining: u8,
}

impl StateTransfer<BigInt> for CthunPatronState {
    fn transfer(&self) -> BTreeMap<Self, BigRational> {
        let mut v = BTreeMap::new();
        if self.remaining == 0 {
            return v;
        }
        let w = self.targets.iter().filter(|&&x| x != 0).count() + self.hero as usize;
        let weight =
            BigRational::from_integer(BigInt::from(1)) / BigRational::from_integer(BigInt::from(w));
        for i in 0..BOARD_SIZE {
            if self.targets[i] <= 0 {
                continue;
            }
            let mut s = *self;
            s.remaining -= 1;
            s.targets[i] -= 1;
            if s.targets[i] == 0 {
                s.targets[i] = -1;
            }
            if i != 0 {
                for i in 0..BOARD_SIZE {
                    if s.targets[i] == 0 {
                        s.targets[i] = 3;
                        break;
                    }
                }
            }
            s.targets.sort_unstable();
            let entry = v.entry(s).or_default();
            *entry += &weight;
        }

        if self.hero > 0 {
            let mut s = *self;
            s.hero -= 1;
            s.remaining -= 1;
            let entry = v.entry(s).or_default();
            *entry += &weight;
        }

        v
    }
}

impl SolvableStateTransfer<BigInt> for CthunPatronState {
    fn end_evaluation(&self) -> BigRational {
        BigRational::from_integer(BigInt::from(self.hero))
    }

    fn stepwise_diff(&self) -> BigRational {
        BigRational::from_integer(BigInt::from(0))
    }
}

impl CthunPatronState {
    pub fn solve() -> BigRational {
        let mut initial_state = CthunPatronState {
            targets: [0; BOARD_SIZE],
            hero: 1,
            remaining: 2,
        };
        initial_state.targets[BOARD_SIZE - 1] = 3;

        let mut cache = BTreeMap::new();
        match initial_state.solve(&mut cache) {
            Some(result) => result,
            None => panic!(),
        }
    }
}
