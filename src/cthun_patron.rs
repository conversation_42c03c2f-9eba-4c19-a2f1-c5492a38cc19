use num_bigint::BigInt;
use num_rational::Ratio;
use num_traits::{One, Zero};
use std::{collections::BTreeMap, str::FromStr};
type Num = BigInt;

use crate::state_calc::{SolvableStateTransfer, StateTransfer};

const BOARD_SIZE: usize = 64;

#[derive(PartialEq, <PERSON>q, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialOrd, Ord)]
pub struct CthunPatronState {
    targets: [i8; BOARD_SIZE],
    hero: i8,
    remaining: i8,
    board_limit: usize,
}

impl StateTransfer<Num> for CthunPatronState {
    fn transfer(&self) -> BTreeMap<Self, Ratio<Num>> {
        let mut v = BTreeMap::new();
        if self.remaining == 0 {
            return v;
        }
        let wm = self.targets.iter().filter(|&&x| x != 0).count();
        let w = wm + if self.hero == 0 { 0 } else { 1 };
        let weight = Ratio::one() / Ratio::from_integer(Num::from(w));
        for i in 0..BOARD_SIZE {
            if self.targets[i] <= 0 {
                continue;
            }
            let mut s = *self;
            s.remaining -= 1;
            s.targets[i] -= 1;
            if s.targets[i] == 0 {
                s.targets[i] = -1;
            } else if wm < s.board_limit {
                for i in 0..BOARD_SIZE {
                    if s.targets[i] == 0 {
                        s.targets[i] = 3;
                        break;
                    }
                }
            }
            s.targets.sort_unstable();
            let entry = v.entry(s).or_default();
            *entry += &weight;
        }

        if self.hero > 0 {
            let mut s = *self;
            s.hero -= 1;
            s.remaining -= 1;
            let entry = v.entry(s).or_default();
            *entry += &weight;
        }

        v
    }
}

impl SolvableStateTransfer<Num> for CthunPatronState {
    fn end_evaluation(&self) -> Ratio<Num> {
        if let Some(v) = self.is_early_break() {
            return v;
        }

        if self.hero > 0 {
            Ratio::one()
        } else {
            Ratio::zero()
        }
    }

    fn stepwise_diff(&self) -> Ratio<Num> {
        Ratio::zero()
    }
}

impl FromStr for CthunPatronState {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        let sp = s
            .split(",")
            .filter_map(|s| s.trim().parse::<i8>().ok())
            .collect::<Vec<_>>();
        let remaining = sp[0];
        let hero = sp[1];
        let board_limit = sp[2];
        let mut targets = sp[3..].to_vec();
        if targets.len() > BOARD_SIZE {
            return Err(format!("Too many targets: {}", targets.len()));
        }
        targets.resize(BOARD_SIZE, 0);
        targets.sort_unstable();
        let targets = targets
            .try_into()
            .map_err(|_| "Failed to convert targets to array")?;
        Ok(CthunPatronState {
            targets,
            hero,
            remaining,
            board_limit: board_limit as usize,
        })
    }
}
