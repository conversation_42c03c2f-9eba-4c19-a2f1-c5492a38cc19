use itertools::Itertools;
use num_bigint::BigInt;
use num_rational::BigRational;
use num_traits::ToPrimitive;
use std::{
    collections::{BTreeMap, HashMap, VecDeque},
    fmt::{Debug, Display},
};

use crate::state_calc::StateTransfer;

const BOARD_SIZE: usize = 64;

#[derive(Clone, Copy, PartialOrd, Ord)]
pub struct GalvadonState {
    step: u8,
    attack: u8,
    keywords: u8,
    windfury: bool,
    pending_choices: [AdaptChoice; 3],
}

impl PartialEq for GalvadonState {
    fn eq(&self, other: &Self) -> bool {
        self.step == other.step
            && self.attack == other.attack
            && self.keywords == other.keywords
            && self.windfury == other.windfury
    }
}

impl Eq for GalvadonState {}

impl std::hash::Hash for GalvadonState {
    fn hash<H: std::hash::Hasher>(&self, state: &mut H) {
        self.step.hash(state);
        self.attack.hash(state);
        self.keywords.hash(state);
        self.windfury.hash(state);
    }
}

#[derive(PartialEq, Eq, Clone, Copy, Hash, PartialOrd, Ord, Debug)]
enum AdaptChoice {
    Windfury,
    Keyword,
    Damage3,
    Damage1,
    Other,
}

const ADAPT_CHOICES: [AdaptChoice; 10] = [
    AdaptChoice::Keyword,
    AdaptChoice::Keyword,
    AdaptChoice::Keyword,
    AdaptChoice::Keyword,
    AdaptChoice::Keyword,
    AdaptChoice::Windfury,
    AdaptChoice::Damage3,
    AdaptChoice::Damage1,
    AdaptChoice::Other,
    AdaptChoice::Other,
];

const UNIQUE_ADAPT_CHOICES: [AdaptChoice; 5] = [
    AdaptChoice::Keyword,
    AdaptChoice::Windfury,
    AdaptChoice::Damage3,
    AdaptChoice::Damage1,
    AdaptChoice::Other,
];

struct AdaptGenerator {
    internal_state: usize,
    base: GalvadonState,
}

impl Iterator for AdaptGenerator {
    type Item = (GalvadonState, GalvadonState, GalvadonState);

    fn next(&mut self) -> Option<(GalvadonState, GalvadonState, GalvadonState)> {
        if self.base.step == 0 {
            return None;
        }
        loop {
            if self.internal_state >= 1000 {
                return None;
            }
            let choices = [
                (self.internal_state / 100) % 10,
                (self.internal_state / 10) % 10,
                self.internal_state % 10,
            ];
            if choices[0] >= choices[1] {
                self.internal_state += 10;
                continue;
            }
            if choices[0] == choices[2] || choices[1] >= choices[2] {
                self.internal_state += 1;
                continue;
            }
            if self.base.keywords as usize > choices[0] {
                self.internal_state += 100;
                continue;
            } else if self.base.keywords as usize > choices[1] {
                self.internal_state += 10;
                continue;
            } else if self.base.keywords as usize > choices[2] {
                self.internal_state += 1;
                continue;
            }
            self.internal_state += 1;
            let choices = [
                (ADAPT_CHOICES[choices[0]]),
                (ADAPT_CHOICES[choices[1]]),
                (ADAPT_CHOICES[choices[2]]),
            ];
            return Some((
                self.base.apply_adapt(choices[0]),
                self.base.apply_adapt(choices[1]),
                self.base.apply_adapt(choices[2]),
            ));
        }
    }
}

impl Debug for GalvadonState {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self)
    }
}

impl Display for GalvadonState {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        write!(
            f,
            "State [{} A{} K{}",
            self.step, self.attack, self.keywords
        )?;
        if self.windfury {
            write!(f, " W")?;
        }
        write!(f, "]")
    }
}

impl GalvadonState {
    fn apply_adapt(&self, choice: AdaptChoice) -> Self {
        match choice {
            AdaptChoice::Keyword => Self {
                keywords: self.keywords + 1,
                step: self.step - 1,
                ..*self
            },
            AdaptChoice::Damage3 => Self {
                attack: self.attack + 3,
                step: self.step - 1,
                ..*self
            },
            AdaptChoice::Damage1 => Self {
                attack: self.attack + 1,
                step: self.step - 1,
                ..*self
            },
            AdaptChoice::Windfury => Self {
                windfury: true,
                step: self.step - 1,
                keywords: self.keywords + 1,
                ..*self
            },
            AdaptChoice::Other => Self {
                step: self.step - 1,
                ..*self
            },
        }
    }

    fn end_evaluation(&self) -> BigRational {
        if self.windfury {
            BigRational::from_integer(BigInt::from(self.attack * 2))
        } else {
            BigRational::from_integer(BigInt::from(self.attack))
        }
    }

    pub fn solve() -> BigRational {
        let initial_state = GalvadonState {
            step: 5,
            attack: 8,
            keywords: 0,
            windfury: false,
            pending_choices: [AdaptChoice::Other; 3],
        };

        fn tf(state: &GalvadonState) -> Vec<(GalvadonState, GalvadonState, GalvadonState)> {
            AdaptGenerator {
                internal_state: 0,
                base: *state,
            }
            .collect::<Vec<_>>()
        }
        let mut tfmap = BTreeMap::new();
        let mut queue = VecDeque::new();
        queue.push_back(initial_state.clone());
        let mut total_children = 0;
        while let Some(state) = queue.pop_front() {
            if tfmap.contains_key(&state) {
                continue;
            }
            let children = tf(&state);
            total_children += children.len();
            for child in children.iter().flat_map(|&v| [v.0, v.1, v.2]) {
                if !tfmap.contains_key(&child) {
                    queue.push_back(child);
                }
            }
            tfmap.insert(state, children);
        }
        // println!("Total children: {}", total_children);
        let mut eval_map = BTreeMap::new();
        for i in 0..5 {
            'lp: for (state, children) in tfmap.iter() {
                if children.is_empty() {
                    eval_map.insert(state, state.end_evaluation());
                    continue;
                }
                let mut total_eval = BigRational::from_integer(BigInt::from(0));
                let mut count = 0;
                for c in children {
                    if !eval_map.contains_key(&c.0)
                        || !eval_map.contains_key(&c.1)
                        || !eval_map.contains_key(&c.2)
                    {
                        continue 'lp;
                    }
                    total_eval += eval_map[&c.0]
                        .clone()
                        .max(eval_map[&c.1].clone())
                        .max(eval_map[&c.2].clone());
                    count += 1;
                }

                eval_map.insert(
                    state,
                    total_eval / BigRational::from_integer(BigInt::from(count)),
                );
            }
            // println!(
            //     "at loop {} we have {} in eval map of total {}",
            //     i,
            //     eval_map.len(),
            //     tfmap.len()
            // );
        }
        // dbg!(eval_map.len());
        // dbg!(&eval_map[&initial_state]);
        for (state, eval) in eval_map.iter() {
            println!("{:?} -> {:?} ({})", state, eval, eval.to_f64().unwrap());
        }
        eval_map[&initial_state].clone()
    }
}
