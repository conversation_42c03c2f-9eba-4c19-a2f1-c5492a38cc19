use itertools::Itertools;
use num_bigint::BigInt;
use num_rational::BigRational;
use std::{
    collections::BTreeMap,
    fmt::{Debug, Display},
};

use crate::state_calc::StateTransfer;

const BOARD_SIZE: usize = 64;

#[derive(PartialEq, Eq, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialOrd, Ord)]
pub struct GalvadonState {
    step: u8,
    attack: u8,
    poisonous: bool,
    divine: bool,
    elusive: bool,
    stealth: bool,
    windfury: bool,
    taunt: bool,
}

enum AdaptChoice {
    Poisonous,
    Divine,
    Elusive,
    Stealth,
    Windfury,
    Taunt,
    Damage3,
    Damage1,
    Health,
    Deathrattle,
}

const AdaptChoices: [AdaptChoice; 10] = [
    AdaptChoice::Poisonous,
    AdaptChoice::Divine,
    AdaptChoice::Elusive,
    AdaptChoice::Stealth,
    AdaptChoice::Windfury,
    AdaptChoice::Taunt,
    AdaptChoice::Damage3,
    AdaptChoice::Damage1,
    AdaptChoice::Health,
    AdaptChoice::Deathrattle,
];

impl StateTransfer for GalvadonState {
    fn transfer(&self) -> BTreeMap<Self, BigRational> {
        let s = *self;
        let mut v = BTreeMap::new();

        for c in (0..3)
            .map(|_| AdaptChoices.iter())
            .multi_cartesian_product()
        {
            if ()
        }
        v
    }

    fn end_evaluation(&self) -> BigRational {
        if self.windfury {
            BigRational::from_integer(BigInt::from(self.attack * 2))
        } else {
            BigRational::from_integer(BigInt::from(self.attack))
        }
    }

    fn stepwise_diff(&self) -> BigRational {
        BigRational::from_integer(BigInt::from(0))
    }
}

impl Debug for GalvadonState {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self)
    }
}

impl Display for GalvadonState {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        write!(f, "State [")?;
        write!(f, "]")
    }
}

impl GalvadonState {
    pub fn solve() -> BigRational {
        let state = GalvadonState::new(board, board_limit, optimal_pop);

        match state.solve(cache) {
            Some(result) => result,
            None => n1,
        }
    }
}
