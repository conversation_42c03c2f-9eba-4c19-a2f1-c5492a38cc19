use ndarray::Array2;
use num_bigint::BigInt;
use num_rational::BigRational;
use num_traits::sign::Signed;
use std::collections::{BTreeMap, VecDeque};
use std::hash::Hash;

pub trait StateTransfer
where
    Self: Eq + <PERSON>h + <PERSON><PERSON> + <PERSON><PERSON> + Ord,
{
    fn transfer(&self) -> BTreeMap<Self, BigRational>;
    fn is_early_break(&self) -> Option<BigRational> {
        None
    }

    fn solve_without_cache(&self) -> Option<BigRational> {
        let mut temp_cache = BTreeMap::new();
        self.solve(&mut temp_cache)
    }

    fn solve(&self, cache: &mut BTreeMap<Self, BigRational>) -> Option<BigRational> {
        solve_markov_chain_with_cache_impl(self.clone(), cache)
    }
}

fn solve_markov_chain_with_cache_impl<T>(
    initial_state: T,
    cache: &mut BTreeMap<T, BigRational>,
) -> Option<BigRational>
where
    T: StateTransfer,
{
    // Check cache first
    if let Some(cached_value) = cache.get(&initial_state) {
        return Some(cached_value.clone());
    }

    // Check for early break on initial state
    if let Some(cached_value) = initial_state.is_early_break() {
        cache.insert(initial_state.clone(), cached_value.clone());
        return Some(cached_value);
    }

    // Build state space and transition table
    let mut state_table = BTreeMap::new();
    let mut queue = VecDeque::with_capacity(64); // Pre-allocate reasonable capacity
    let mut early_break_states = BTreeMap::new(); // Track states with known values

    queue.push_back(initial_state.clone());

    while let Some(state) = queue.pop_front() {
        if state_table.contains_key(&state) || early_break_states.contains_key(&state) {
            continue;
        }

        // Check cache for this state
        if let Some(cached_value) = cache.get(&state) {
            early_break_states.insert(state, cached_value.clone());
            continue;
        }

        // Check for early break
        if let Some(cached_value) = state.is_early_break() {
            early_break_states.insert(state.clone(), cached_value.clone());
            cache.insert(state, cached_value);
            continue; // Don't explore transitions from this state
        }

        let transitions = state.transfer();

        // Add new states to queue
        for next_state in transitions.keys() {
            if !state_table.contains_key(next_state) && !early_break_states.contains_key(next_state)
            {
                queue.push_back(next_state.clone());
            }
        }

        state_table.insert(state, transitions);
    }

    let states: Vec<_> = state_table.keys().cloned().collect();
    let n = states.len();

    if n == 0 {
        // Only early break states exist, return cached value for initial state
        return early_break_states.get(&initial_state).cloned();
    }

    // Create state index mapping (only for non-early-break states)
    let state_to_index: BTreeMap<_, _> = states
        .iter()
        .enumerate()
        .map(|(i, state)| (state.clone(), i))
        .collect();

    // Build system of equations: E[state] = 1 + sum(prob * E[next_state])
    // Rearranged as: E[state] - sum(prob * E[next_state]) = 1
    let mut matrix = Array2::zeros((n, n + 1));

    for (i, state) in states.iter().enumerate() {
        matrix[[i, i]] = BigRational::from_integer(BigInt::from(1)); // Coefficient of E[state]
        matrix[[i, n]] = BigRational::from_integer(BigInt::from(1)); // RHS = 1

        let transitions = &state_table[state];

        // If no transitions (absorbing state), equation is E[state] = 0
        if transitions.is_empty() {
            matrix[[i, i]] = BigRational::from_integer(BigInt::from(1));
            matrix[[i, n]] = BigRational::from_integer(BigInt::from(0));
        } else {
            // For each transition, handle both regular states and early break states
            for (next_state, prob) in transitions.iter() {
                if let Some(&j) = state_to_index.get(next_state) {
                    // Regular state - subtract prob * E[next_state]
                    matrix[[i, j]] -= prob;
                } else if let Some(cached_value) = early_break_states.get(next_state) {
                    // Early break state - add prob * cached_value to RHS
                    let contribution = prob * cached_value;
                    matrix[[i, n]] += contribution;
                }
            }
        }
    }

    // Solve using Gaussian elimination and cache all computed results
    if let Some(solution) = gaussian_solve(&mut matrix) {
        // Cache all computed state values
        for (i, state) in states.iter().enumerate() {
            cache.insert(state.clone(), solution[i].clone());
        }

        if let Some(&index) = state_to_index.get(&initial_state) {
            Some(solution[index].clone())
        } else {
            // Initial state was an early break state
            early_break_states.get(&initial_state).cloned()
        }
    } else {
        None
    }
}

fn gaussian_solve(matrix: &mut Array2<BigRational>) -> Option<Vec<BigRational>> {
    let (n, m) = matrix.dim();
    let zero = BigRational::from_integer(BigInt::from(0));

    // Track non-zero elements in each row for sparse optimization
    let mut non_zero_cols: Vec<Vec<usize>> = Vec::with_capacity(n);
    for i in 0..n {
        let mut nz_cols = Vec::with_capacity(m); // Pre-allocate capacity
        for j in 0..m {
            if matrix[[i, j]] != zero {
                nz_cols.push(j);
            }
        }
        non_zero_cols.push(nz_cols);
    }

    // Forward elimination
    for i in 0..n {
        // Find pivot - only check rows that have non-zero in column i
        let mut pivot = i;
        let mut max_abs = matrix[[i, i]].abs();

        for k in (i + 1)..n {
            if non_zero_cols[k].contains(&i) {
                let abs_val = matrix[[k, i]].abs();
                if abs_val > max_abs {
                    pivot = k;
                    max_abs = abs_val;
                }
            }
        }

        // Swap rows if needed
        if pivot != i {
            // Swap matrix rows efficiently - only swap non-zero elements
            let mut all_cols =
                Vec::with_capacity(non_zero_cols[i].len() + non_zero_cols[pivot].len());
            all_cols.extend_from_slice(&non_zero_cols[i]);
            for &col in &non_zero_cols[pivot] {
                if !all_cols.contains(&col) {
                    all_cols.push(col);
                }
            }

            for &j in &all_cols {
                matrix.swap([i, j], [pivot, j]);
            }

            // Swap non-zero tracking
            non_zero_cols.swap(i, pivot);
        }

        // Check for zero pivot
        if matrix[[i, i]] == zero {
            return None;
        }

        // Eliminate column - only process rows that have non-zero in column i
        for k in (i + 1)..n {
            if !non_zero_cols[k].contains(&i) {
                continue; // Skip if already zero
            }

            let factor = &matrix[[k, i]] / &matrix[[i, i]];

            // Update row k: row_k = row_k - factor * row_i
            // Only process columns that are non-zero in either row
            let mut new_nz_cols = Vec::with_capacity(non_zero_cols[k].len());

            // Collect all columns that might become non-zero
            let mut all_cols = Vec::with_capacity(non_zero_cols[k].len() + non_zero_cols[i].len());
            for &col in &non_zero_cols[k] {
                if col >= i {
                    all_cols.push(col);
                }
            }
            for &col in &non_zero_cols[i] {
                if col >= i && !all_cols.contains(&col) {
                    all_cols.push(col);
                }
            }

            // Process the combined columns
            for &j in &all_cols {
                let temp = &factor * &matrix[[i, j]];
                matrix[[k, j]] -= temp;

                if matrix[[k, j]] != zero {
                    new_nz_cols.push(j);
                }
            }

            // Keep columns before i that were already non-zero
            for &col in &non_zero_cols[k] {
                if col < i {
                    new_nz_cols.push(col);
                }
            }

            // Update non-zero tracking for row k
            new_nz_cols.sort_unstable();
            new_nz_cols.dedup();
            non_zero_cols[k] = new_nz_cols;
        }
    }

    // Back substitution - optimized for sparse structure
    let mut solution = Vec::with_capacity(n);
    solution.resize(n, zero.clone());
    for i in (0..n).rev() {
        if matrix[[i, i]] == zero {
            return None;
        }

        solution[i] = matrix[[i, m - 1]].clone();

        // Only subtract for non-zero coefficients
        for &j in &non_zero_cols[i] {
            if j > i && j < n {
                let temp = &matrix[[i, j]] * &solution[j];
                solution[i] -= temp;
            }
        }

        solution[i] /= &matrix[[i, i]];
    }

    Some(solution)
}
