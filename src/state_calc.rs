use ndarray::Array2;
use num_bigint::BigInt;
use num_integer::Integer;
use num_rational::Ratio;
use num_traits::ToPrimitive;
use num_traits::sign::Signed;
use num_traits::{One, Zero};
use std::collections::{BTreeMap, VecDeque};
use std::hash::Hash;
use std::ops::{AddAssign, DivAssign, Mul, SubAssign};

pub trait StateTransfer<N>
where
    Self: Eq + Hash + Sized + Clone + Ord,
    N: Clone + Zero,
{
    fn transfer(&self) -> BTreeMap<Self, Ratio<N>>;

    fn is_early_break(&self) -> Option<Ratio<N>> {
        None
    }

    fn build_transition_table(
        &self,
        cache: &mut BTreeMap<Self, Ratio<N>>,
    ) -> (
        BTreeMap<Self, BTreeMap<Self, Ratio<N>>>,
        BTreeMap<Self, Ratio<N>>,
    ) {
        let mut state_table = BTreeMap::new();
        let mut queue = VecDeque::with_capacity(64); // Pre-allocate reasonable capacity
        let mut early_break_states = BTreeMap::new(); // Track states with known values

        queue.push_back(self.clone());

        while let Some(state) = queue.pop_front() {
            if state_table.contains_key(&state) || early_break_states.contains_key(&state) {
                continue;
            }

            // Check cache for this state
            if let Some(cached_value) = cache.get(&state) {
                early_break_states.insert(state, cached_value.clone());
                continue;
            }

            // Check for early break
            if let Some(cached_value) = state.is_early_break() {
                early_break_states.insert(state.clone(), cached_value.clone());
                cache.insert(state, cached_value);
                continue; // Don't explore transitions from this state
            }

            let transitions = state.transfer();
            let transitions = transitions
                .into_iter()
                .filter(|(_k, v)| !v.numer().is_zero())
                .collect::<BTreeMap<_, _>>();

            // Add new states to queue
            for next_state in transitions.keys() {
                if !state_table.contains_key(next_state)
                    && !early_break_states.contains_key(next_state)
                {
                    queue.push_back(next_state.clone());
                }
            }

            state_table.insert(state, transitions);
        }

        (state_table, early_break_states)
    }
}

pub trait SolvableStateTransfer<N>
where
    Self: StateTransfer<N>,
    N: Clone + Mul + One + Integer + Signed + AddAssign,
    Ratio<N>:
        AddAssign + SubAssign + for<'a> SubAssign<&'a Ratio<N>> + for<'a> DivAssign<&'a Ratio<N>>,
{
    fn end_evaluation(&self) -> Ratio<N>;
    fn stepwise_diff(&self) -> Ratio<N>;

    fn solve(&self, cache: &mut BTreeMap<Self, Ratio<N>>) -> Option<Ratio<N>> {
        // Check cache first
        if let Some(cached_value) = cache.get(&self) {
            return Some(cached_value.clone());
        }

        // Check for early break on initial state
        if let Some(cached_value) = self.is_early_break() {
            cache.insert(self.clone(), cached_value.clone());
            return Some(cached_value);
        }

        // Build state space and transition table

        #[cfg(feature = "measure_time")]
        let now = std::time::Instant::now();

        let (state_table, early_break_states) = self.build_transition_table(cache);

        let cmplx1 = state_table.len();
        let cmplx2 = state_table.values().map(|v| v.len()).sum::<usize>();
        #[cfg(feature = "measure_time")]
        println!(
            "Complexity: {} @ {} ({:.2}%) (took {:?})",
            cmplx1,
            cmplx2,
            Ratio::new(
                BigInt::from(cmplx2) * BigInt::from(100),
                BigInt::from(cmplx1) * BigInt::from(cmplx1 + 1)
            )
            .to_f64()
            .unwrap(),
            now.elapsed()
        );
        #[cfg(not(feature = "measure_time"))]
        println!(
            "Complexity: {} @ {} ({:.2}%)",
            cmplx1,
            cmplx2,
            Ratio::new(
                BigInt::from(cmplx2) * BigInt::from(100),
                BigInt::from(cmplx1) * BigInt::from(cmplx1 + 1)
            )
            .to_f64()
            .unwrap()
        );

        if state_table.len() == 0 {
            // Only early break states exist, return cached value for initial state
            panic!("We should already have handled this case");
            // return early_break_states.get(&self).cloned();
        }

        manual_solve(self, &state_table, &early_break_states, cache)
    }

    #[cfg(feature = "symbolica_backend")]
    fn solve_symbolic(&self) -> Option<Ratio<N>>
    where
        N: std::fmt::Display + From<i64>,
    {
        use symbolica::atom::{Atom, AtomCore, AtomView, InlineVar};

        let mut cache = BTreeMap::new();
        if let Some(cached_value) = self.is_early_break() {
            cache.insert(self.clone(), cached_value.clone());
            return Some(cached_value);
        }

        let (state_table, early_break_states) = self.build_transition_table(&mut cache);

        let symbols = state_table
            .keys()
            .enumerate()
            .map(|(i, state)| {
                let id = format!("x{}", i);
                let sym = symbolica::symbol!(id.clone());
                (state.clone(), sym)
            })
            .collect::<BTreeMap<_, _>>();

        let mut eqs = Vec::new();
        for (i, state) in state_table.iter() {
            let lhs = symbols[i].clone();
            if state.len() == 0 {
                eqs.push(format!("{} - {}", i.end_evaluation(), lhs));
            } else {
                // state = sum(prob * next_state) + stepwise_diff
                let rhs = state
                    .iter()
                    .map(|(next_state, prob)| {
                        let early = early_break_states.get(next_state);
                        if let Some(early_value) = early {
                            format!("({} * {})", prob, early_value)
                        } else {
                            format!("({} * {})", prob, symbols[next_state])
                        }
                    })
                    .join(" + ");
                eqs.push(format!("{} - ({} + {})", lhs, i.stepwise_diff(), rhs));
            }
        }
        let variables = symbols
            .values()
            .map(|s| s.clone().into())
            .collect::<Vec<InlineVar>>();
        let system = eqs
            .iter()
            .map(|s| {
                let tp = symbolica::try_parse!(s);
                if let Err(e) = tp {
                    panic!("Failed to parse: {} - {:?}", s, e);
                }
                tp.unwrap()
            })
            .collect::<Vec<_>>();
        let sol = AtomView::solve_linear_system::<u8, _, _>(&system, &variables[..]).unwrap();
        for (i, v) in sol.iter().enumerate() {
            if symbols.keys().nth(i).unwrap() == self {
                if let Atom::Num(n) = v {
                    let cv = n.to_num_view().get_coeff_view();
                    match cv {
                        symbolica::coefficient::CoefficientView::Natural(n, d, _, _) => {
                            return Some(Ratio::new(N::from(n), N::from(d)));
                        }
                        symbolica::coefficient::CoefficientView::Float(
                            serialized_float,
                            serialized_float1,
                        ) => todo!(),
                        symbolica::coefficient::CoefficientView::Large(
                            serialized_rational,
                            serialized_rational1,
                        ) => todo!(),
                        symbolica::coefficient::CoefficientView::FiniteField(
                            finite_field_element,
                            finite_field_index,
                        ) => todo!(),
                        symbolica::coefficient::CoefficientView::RationalPolynomial(
                            serialized_rational_polynomial,
                        ) => todo!(),
                    }
                }
            }
        }

        None
    }

    fn parse_and_solve(s: &str) -> Ratio<N>
    where
        Self: std::str::FromStr,
        <Self as std::str::FromStr>::Err: std::fmt::Debug,
    {
        let initial_state = Self::from_str(s).unwrap();
        let mut cache = BTreeMap::new();
        initial_state.solve(&mut cache).unwrap()
    }
}

fn manual_solve<N, S>(
    initial_state: &S,
    state_table: &BTreeMap<S, BTreeMap<S, Ratio<N>>>,
    early_break_states: &BTreeMap<S, Ratio<N>>,
    cache: &mut BTreeMap<S, Ratio<N>>,
) -> Option<Ratio<N>>
where
    S: Ord + Clone + SolvableStateTransfer<N>,
    N: Clone + Mul + One + Integer + Signed + AddAssign,
    Ratio<N>:
        AddAssign + SubAssign + for<'a> SubAssign<&'a Ratio<N>> + for<'a> DivAssign<&'a Ratio<N>>,
{
    #[cfg(feature = "measure_time")]
    let now = std::time::Instant::now();

    let states = state_table.keys().cloned().collect::<Vec<_>>();
    // Create state index mapping (only for non-early-break states)
    let state_to_index = states
        .iter()
        .enumerate()
        .map(|(i, state)| (state.clone(), i))
        .collect::<BTreeMap<_, _>>();
    let n = states.len();
    // Build system of equations: E[state] = state.stepwise_diff() + sum(prob * E[next_state])
    // Rearranged as: E[state] - sum(prob * E[next_state]) = state.stepwise_diff()
    let mut matrix = Array2::zeros((n, n + 1));

    #[cfg(feature = "measure_time")]
    println!("Zeroed matrix (took {:?})", now.elapsed());

    #[cfg(feature = "measure_time")]
    let now = std::time::Instant::now();

    for (i, state) in states.iter().enumerate() {
        matrix[[i, i]] = Ratio::from_integer(N::one()); // Coefficient of E[state]
        matrix[[i, n]] = state.stepwise_diff(); // RHS = state.stepwise_diff()

        let transitions = &state_table[state];

        // If no transitions (absorbing state), equation is E[state] = state.end_evaluation()
        if transitions.is_empty() {
            matrix[[i, i]] = Ratio::from_integer(N::one());
            matrix[[i, n]] = state.end_evaluation();
        } else {
            // For each transition, handle both regular states and early break states
            for (next_state, prob) in transitions.iter() {
                if let Some(&j) = state_to_index.get(next_state) {
                    // Regular state - subtract prob * E[next_state]
                    matrix[[i, j]] -= prob;
                } else if let Some(cached_value) = early_break_states.get(next_state) {
                    // Early break state - add prob * cached_value to RHS
                    let contribution = prob * cached_value;
                    matrix[[i, n]] += contribution;
                }
            }
        }
    }

    #[cfg(feature = "measure_time")]
    println!("Initialized matrix (took {:?})", now.elapsed());

    #[cfg(feature = "measure_time")]
    let now = std::time::Instant::now();

    // Solve using Gaussian elimination and cache all computed results
    if let Some(solution) = gaussian_solve(&mut matrix) {
        #[cfg(feature = "measure_time")]
        println!("Solved matrix (took {:?})", now.elapsed());
        // Cache all computed state values
        for (i, state) in states.iter().enumerate() {
            cache.insert(state.clone(), solution[i].clone());
        }

        if let Some(&index) = state_to_index.get(&initial_state) {
            Some(solution[index].clone())
        } else {
            // Initial state was an early break state
            early_break_states.get(&initial_state).cloned()
        }
    } else {
        #[cfg(feature = "measure_time")]
        println!("Failed to solve matrix (took {:?})", now.elapsed());
        None
    }
}

fn gaussian_solve<T>(matrix: &mut Array2<Ratio<T>>) -> Option<Vec<Ratio<T>>>
where
    T: Zero + PartialEq + Clone + Integer + Signed,
    Ratio<T>: SubAssign + for<'a> DivAssign<&'a Ratio<T>>,
{
    let (n, m) = matrix.dim();
    let zero = Ratio::from_integer(T::zero());

    // Track non-zero elements in each row for sparse optimization
    let mut non_zero_cols: Vec<Vec<usize>> = Vec::with_capacity(n);
    for i in 0..n {
        let mut nz_cols = Vec::with_capacity(m); // Pre-allocate capacity
        for j in 0..m {
            if matrix[[i, j]] != zero {
                nz_cols.push(j);
            }
        }
        non_zero_cols.push(nz_cols);
    }

    // Forward elimination
    for i in 0..n {
        // Find pivot - only check rows that have non-zero in column i
        let mut pivot = i;
        let mut max_abs = matrix[[i, i]].abs();

        for k in (i + 1)..n {
            if non_zero_cols[k].contains(&i) {
                let abs_val = matrix[[k, i]].abs();
                if abs_val > max_abs {
                    pivot = k;
                    max_abs = abs_val;
                }
            }
        }

        // Swap rows if needed
        if pivot != i {
            // Swap matrix rows efficiently - only swap non-zero elements
            let mut all_cols =
                Vec::with_capacity(non_zero_cols[i].len() + non_zero_cols[pivot].len());
            all_cols.extend_from_slice(&non_zero_cols[i]);
            for &col in &non_zero_cols[pivot] {
                if !all_cols.contains(&col) {
                    all_cols.push(col);
                }
            }

            for &j in &all_cols {
                matrix.swap([i, j], [pivot, j]);
            }

            // Swap non-zero tracking
            non_zero_cols.swap(i, pivot);
        }

        // Check for zero pivot
        if matrix[[i, i]] == zero {
            return None;
        }

        // Eliminate column - only process rows that have non-zero in column i
        for k in (i + 1)..n {
            if !non_zero_cols[k].contains(&i) {
                continue; // Skip if already zero
            }

            let factor = &matrix[[k, i]] / &matrix[[i, i]];

            // Update row k: row_k = row_k - factor * row_i
            // Only process columns that are non-zero in either row
            let mut new_nz_cols = Vec::with_capacity(non_zero_cols[k].len());

            // Collect all columns that might become non-zero
            let mut all_cols = Vec::with_capacity(non_zero_cols[k].len() + non_zero_cols[i].len());
            for &col in &non_zero_cols[k] {
                if col >= i {
                    all_cols.push(col);
                }
            }
            for &col in &non_zero_cols[i] {
                if col >= i && !all_cols.contains(&col) {
                    all_cols.push(col);
                }
            }

            // Process the combined columns
            for &j in &all_cols {
                let temp = &factor * &matrix[[i, j]];
                matrix[[k, j]] -= temp;

                if matrix[[k, j]] != zero {
                    new_nz_cols.push(j);
                }
            }

            // Simpliy row
            {
                let mut numer_gcd = Option::<T>::None;
                let mut denom_gcd = Option::<T>::None;
                for j in 0..m {
                    if matrix[[k, j]] != zero {
                        let num = matrix[[k, j]].numer().abs();
                        numer_gcd = Some(match numer_gcd {
                            None => num,
                            Some(existing) => existing.gcd(&num),
                        });
                        let den = matrix[[k, j]].denom().abs();
                        denom_gcd = Some(match denom_gcd {
                            None => den,
                            Some(existing) => existing.gcd(&den),
                        });
                    }
                }
                if numer_gcd.is_some() || denom_gcd.is_some() {
                    let numer_gcd = numer_gcd.unwrap_or_else(T::one);
                    let denom_gcd = denom_gcd.unwrap_or_else(T::one);
                    if numer_gcd != T::one() || denom_gcd != T::one() {
                        let final_gcd = Ratio::from_integer(numer_gcd) / denom_gcd;
                        for j in 0..m {
                            if matrix[[k, j]] != zero {
                                matrix[[k, j]] /= &final_gcd;
                            }
                        }
                    }
                }
            }

            // Keep columns before i that were already non-zero
            for &col in &non_zero_cols[k] {
                if col < i {
                    new_nz_cols.push(col);
                }
            }

            // Update non-zero tracking for row k
            new_nz_cols.sort_unstable();
            new_nz_cols.dedup();
            non_zero_cols[k] = new_nz_cols;
        }
    }

    // Back substitution - optimized for sparse structure
    let mut solution = Vec::with_capacity(n);
    solution.resize(n, zero.clone());
    for i in (0..n).rev() {
        if matrix[[i, i]] == zero {
            return None;
        }

        solution[i] = matrix[[i, m - 1]].clone();

        // Only subtract for non-zero coefficients
        for &j in &non_zero_cols[i] {
            if j > i && j < n {
                let temp = &matrix[[i, j]] * &solution[j];
                solution[i] -= temp;
            }
        }

        solution[i] /= &matrix[[i, i]];
    }

    Some(solution)
}

pub fn gaussian_solve_on_map<N, S>(
    initial_state: &S,
    orig_state_table: &BTreeMap<S, BTreeMap<S, Ratio<N>>>,
    early_break_states: &BTreeMap<S, Ratio<N>>,
    cache: &mut BTreeMap<S, Ratio<N>>,
) -> Option<Ratio<N>>
where
    S: Ord + Clone + SolvableStateTransfer<N>,
    N: Clone + Mul + One + Integer + Signed + AddAssign,
    Ratio<N>:
        AddAssign + SubAssign + for<'a> SubAssign<&'a Ratio<N>> + for<'a> DivAssign<&'a Ratio<N>>,
{
    // For now, just call the matrix-based version to ensure correctness
    // TODO: Implement the actual sparse version
    let states = orig_state_table.keys().cloned().collect::<Vec<_>>();
    let state_to_index = states
        .iter()
        .enumerate()
        .map(|(i, state)| (state.clone(), i))
        .collect::<BTreeMap<_, _>>();
    let n = states.len();

    if n == 0 {
        return early_break_states.get(initial_state).cloned();
    }

    // Build matrix exactly like the original implementation
    let mut matrix = Array2::zeros((n, n + 1));

    for (i, state) in states.iter().enumerate() {
        matrix[[i, i]] = Ratio::from_integer(N::one()); // Coefficient of E[state]
        matrix[[i, n]] = state.stepwise_diff(); // RHS = state.stepwise_diff()

        let transitions = &orig_state_table[state];

        // If no transitions (absorbing state), equation is E[state] = state.end_evaluation()
        if transitions.is_empty() {
            matrix[[i, i]] = Ratio::from_integer(N::one());
            matrix[[i, n]] = state.end_evaluation();
        } else {
            // For each transition, handle both regular states and early break states
            for (next_state, prob) in transitions.iter() {
                if let Some(&j) = state_to_index.get(next_state) {
                    // Regular state - subtract prob * E[next_state]
                    matrix[[i, j]] -= prob;
                } else if let Some(cached_value) = early_break_states.get(next_state) {
                    // Early break state - add prob * cached_value to RHS
                    let contribution = prob * cached_value;
                    matrix[[i, n]] += contribution;
                }
            }
        }
    }

    // Solve using Gaussian elimination and cache all computed results
    if let Some(solution) = gaussian_solve(&mut matrix) {
        // Cache all computed state values
        for (i, state) in states.iter().enumerate() {
            cache.insert(state.clone(), solution[i].clone());
        }

        if let Some(&index) = state_to_index.get(&initial_state) {
            Some(solution[index].clone())
        } else {
            // Initial state was an early break state
            early_break_states.get(&initial_state).cloned()
        }
    } else {
        None
    }
}

// Helper function to apply GCD reduction to a single equation
fn apply_gcd_reduction_to_equation<N, S>(
    state: &S,
    state_table: &mut BTreeMap<S, BTreeMap<S, Ratio<N>>>,
    eq_consts: &mut BTreeMap<S, Ratio<N>>,
) where
    S: Ord + Clone,
    N: Clone + One + Integer + Signed,
    Ratio<N>: for<'a> DivAssign<&'a Ratio<N>>,
{
    let zero = Ratio::from_integer(N::zero());
    let mut numer_gcd = Option::<N>::None;
    let mut denom_gcd = Option::<N>::None;

    // Collect all coefficients including the constant
    let mut all_coeffs = Vec::new();

    // Add transition coefficients
    for coeff in state_table[state].values() {
        if *coeff != zero {
            all_coeffs.push(coeff.clone());
        }
    }

    // Add constant term
    if eq_consts[state] != zero {
        all_coeffs.push(eq_consts[state].clone());
    }

    // Calculate GCD of numerators and denominators
    for coeff in &all_coeffs {
        let num = coeff.numer().abs();
        numer_gcd = Some(match numer_gcd {
            None => num,
            Some(existing) => existing.gcd(&num),
        });

        let den = coeff.denom().abs();
        denom_gcd = Some(match denom_gcd {
            None => den,
            Some(existing) => existing.gcd(&den),
        });
    }

    if let (Some(ngcd), Some(dgcd)) = (numer_gcd, denom_gcd) {
        if ngcd != N::one() || dgcd != N::one() {
            let reduction_factor = Ratio::from_integer(ngcd) / dgcd;

            // Apply reduction to all coefficients
            for coeff in state_table.get_mut(state).unwrap().values_mut() {
                if *coeff != zero {
                    *coeff /= &reduction_factor;
                }
            }

            // Apply reduction to constant
            if eq_consts[state] != zero {
                *eq_consts.get_mut(state).unwrap() /= &reduction_factor;
            }
        }
    }
}
