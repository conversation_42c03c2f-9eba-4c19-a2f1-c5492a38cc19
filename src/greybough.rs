use num_bigint::BigInt;
use num_rational::BigRational;
use std::{
    collections::BTreeMap,
    fmt::{Debug, Display},
};

use crate::state_calc::StateTransfer;

const BOARD_SIZE: usize = 64;

#[derive(PartialEq, <PERSON>q, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialOrd, Ord)]
pub struct GreyboughState {
    board: [u8; BOARD_SIZE],
    limit: u8,
    optimal_pop: bool,
}

impl StateTransfer for GreyboughState {
    fn transfer(&self) -> BTreeMap<Self, BigRational> {
        let s = *self;
        let start = BOARD_SIZE - s.limit as usize;
        if s.optimal_pop {
            let nonzeroi = (start..BOARD_SIZE).find(|&i| s.board[i] != 0);
            if nonzeroi.is_none() {
                return BTreeMap::new();
            }
            let i = nonzeroi.unwrap();

            return self.pop_nth(i);
        } else {
            let mut v = BTreeMap::new();
            let choice_count = (start..BOARD_SIZE).filter(|&i| s.board[i] != 0).count();

            if choice_count == 0 {
                return v;
            }

            let prob_per_choice = BigRational::from_integer(BigInt::from(1))
                / BigRational::from_integer(BigInt::from(choice_count));

            for i in (start..BOARD_SIZE).filter(|&i| s.board[i] != 0) {
                let t = self.pop_nth(i);
                for (state, weight) in t.iter() {
                    let entry = v.entry(*state).or_default();
                    *entry += weight * &prob_per_choice;
                }
            }

            v
        }
    }

    fn is_early_break(&self) -> Option<BigRational> {
        if !self.optimal_pop {
            for i in 0..self.board.len() {
                if self.board[i] == 0 {
                    continue;
                }
                if i == self.board.len() - 1 {
                    let mut v = BigRational::from_integer(BigInt::from(1));
                    for _ in 1..self.board[i] {
                        v *= BigRational::from_integer(BigInt::from(2));
                    }
                    //println!("Early break (1 elem): {} -> {}", self, &v);
                    return Some(v);
                }
                if self.board[i] == 1 {
                    let ones_after = self.board[i + 1..].iter().filter(|&&x| x == 1).count();
                    if ones_after == self.board.len() - i - 1 {
                        let mut v = BigRational::from_integer(BigInt::from(2));
                        for _ in 0..ones_after {
                            v *= BigRational::from_integer(BigInt::from(2));
                        }
                        v -= BigRational::from_integer(BigInt::from(1));
                        //println!("Early break (1s after): {} -> {}", self, &v);
                        return Some(v);
                    }
                }
                break;
            }
        }
        None
    }

    fn end_evaluation(&self) -> BigRational {
        BigRational::from_integer(BigInt::from(0))
    }

    fn stepwise_diff(&self) -> BigRational {
        BigRational::from_integer(BigInt::from(1))
    }
}

impl Debug for GreyboughState {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self)
    }
}

impl Display for GreyboughState {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        write!(f, "State [")?;
        for i in 0..self.board.len() {
            write!(f, "{}", self.board[i])?;
            if i != self.board.len() - 1 {
                write!(f, ",")?;
            }
        }
        if self.optimal_pop {
            write!(f, " (OP)")?;
        }
        write!(f, "]")
    }
}

impl GreyboughState {
    pub fn pop_nth(&self, n: usize) -> BTreeMap<Self, BigRational> {
        let mut v = BTreeMap::new();
        let mut s = *self;
        let start = BOARD_SIZE - s.limit as usize;
        let pops = s.board[n];
        s.board[n] = 0;

        let mut seed_target = Vec::with_capacity(BOARD_SIZE);
        for i in start..BOARD_SIZE {
            if s.board[i] != 0 {
                seed_target.push(Some(i));
            }
        }

        if seed_target.is_empty() {
            seed_target.push(None);
        }

        let one = BigRational::from_integer(BigInt::from(1));

        for st in seed_target {
            let mut s = s;
            if let Some(st) = st {
                s.board[st] += 1;
            }

            let mut pops = pops;
            for j in start..BOARD_SIZE {
                if s.board[j] == 0 && pops > 1 {
                    s.board[j] = 1;
                    pops -= 1;
                } else {
                    break;
                }
            }

            s.sort();
            *v.entry(s)
                .or_insert_with(|| BigRational::from_integer(BigInt::from(0))) += &one;
        }

        let total_weight = v
            .values()
            .fold(BigRational::from_integer(BigInt::from(0)), |acc, val| {
                acc + val
            });
        for val in v.values_mut() {
            *val /= &total_weight;
        }

        v
    }

    pub fn new(v: &Vec<u8>, limit: u8, optimal_pop: bool) -> Self {
        let mut s = GreyboughState {
            board: [0; BOARD_SIZE],
            limit,
            optimal_pop,
        };
        s.board[..v.len()].copy_from_slice(&v[..]);
        s.sort();
        s
    }

    fn sort(&mut self) {
        self.board.sort_unstable();
    }

    /// board.len <= 25; board.sum() <= 35; board_limit <= 25
    pub fn solve_without_cache(
        board: &Vec<u8>,
        board_limit: usize,
        optimal_pop: bool,
    ) -> BigRational {
        let mut cache = BTreeMap::new();
        Self::solve(board, board_limit, optimal_pop, &mut cache)
    }

    pub fn solve(
        board: &Vec<u8>,
        board_limit: usize,
        optimal_pop: bool,
        cache: &mut BTreeMap<GreyboughState, BigRational>,
    ) -> BigRational {
        if board_limit > BOARD_SIZE {
            panic!("Board limit too large");
        }
        let sum: u16 = board.iter().map(|&x| x as u16).sum();
        let n1 = BigRational::from_integer(BigInt::from(-1));
        if board.len() > 25 || sum > 35 {
            return n1;
        }

        if sum == 0 {
            return n1;
        }

        let board_limit = std::cmp::min(board_limit as u8, sum as u8);
        let board_limit = std::cmp::min(board_limit, 25);

        let state = GreyboughState::new(board, board_limit, optimal_pop);

        match state.solve(cache) {
            Some(result) => result,
            None => n1,
        }
    }
}
