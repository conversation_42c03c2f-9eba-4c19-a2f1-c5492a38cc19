use num_bigint::BigInt;
use num_rational::Ratio;
use num_traits::{One, Zero};
use std::{
    collections::BTreeMap,
    fmt::{Debug, Display},
    str::FromStr,
};
type Num = BigInt;

use crate::state_calc::{SolvableStateTransfer, StateTransfer};

const BOARD_SIZE: usize = 64;

#[derive(PartialEq, Eq, <PERSON>lone, Copy, Hash, PartialOrd, Ord)]
pub struct GreyboughState {
    board: [u8; BOARD_SIZE],
    limit: u8,
    optimal_pop: bool,
}

impl StateTransfer<Num> for GreyboughState {
    fn is_early_break(&self) -> Option<Ratio<Num>> {
        if !self.optimal_pop {
            for i in 0..self.board.len() {
                if self.board[i] == 0 {
                    continue;
                }
                if i == self.board.len() - 1 {
                    let mut v = Ratio::one();
                    for _ in 1..self.board[i] {
                        v *= Ratio::from_integer(Num::from(2));
                    }
                    //println!("Early break (1 elem): {} -> {}", self, &v);
                    return Some(v);
                }
                if self.board[i] == 1 {
                    let ones_after = self.board[i + 1..].iter().filter(|&&x| x == 1).count();
                    if ones_after == self.board.len() - i - 1 {
                        let mut v = Ratio::from_integer(Num::from(2));
                        for _ in 0..ones_after {
                            v *= Ratio::from_integer(Num::from(2));
                        }
                        v -= Ratio::one();
                        //println!("Early break (1s after): {} -> {}", self, &v);
                        return Some(v);
                    }
                }
                break;
            }
        }
        None
    }

    fn transfer(&self) -> BTreeMap<Self, Ratio<Num>> {
        let s = *self;
        let start = BOARD_SIZE - s.limit as usize;
        if s.optimal_pop {
            let nonzeroi = (start..BOARD_SIZE).find(|&i| s.board[i] != 0);
            if nonzeroi.is_none() {
                return BTreeMap::new();
            }
            let i = nonzeroi.unwrap();

            return self.pop_nth(i);
        } else {
            let mut v = BTreeMap::new();
            let choice_count = (start..BOARD_SIZE).filter(|&i| s.board[i] != 0).count();

            if choice_count == 0 {
                return v;
            }

            let prob_per_choice = Ratio::one() / Ratio::from_integer(Num::from(choice_count));

            for i in (start..BOARD_SIZE).filter(|&i| s.board[i] != 0) {
                let t = self.pop_nth(i);
                for (state, weight) in t.iter() {
                    let entry = v.entry(*state).or_default();
                    *entry += weight * &prob_per_choice;
                }
            }

            v
        }
    }
}

impl SolvableStateTransfer<Num> for GreyboughState {
    fn end_evaluation(&self) -> Ratio<Num> {
        if let Some(v) = self.is_early_break() {
            return v;
        }
        Ratio::zero()
    }

    fn stepwise_diff(&self) -> Ratio<Num> {
        Ratio::one()
    }
}

impl Debug for GreyboughState {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self)
    }
}

impl Display for GreyboughState {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        write!(f, "State [")?;
        for i in 0..self.board.len() {
            write!(f, "{}", self.board[i])?;
            if i != self.board.len() - 1 {
                write!(f, ",")?;
            }
        }
        if self.optimal_pop {
            write!(f, " (OP)")?;
        }
        write!(f, "]")
    }
}

impl FromStr for GreyboughState {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        let optimal = s.starts_with(&['O', 'o']);
        let s = if optimal { &s[1..] } else { s }
            .split(",")
            .filter_map(|s| s.trim().parse::<u8>().ok())
            .collect::<Vec<_>>();
        let limit = s[0];
        let s = s[1..].to_vec();
        Ok(GreyboughState::new(&s, limit, optimal))
    }
}

impl GreyboughState {
    pub fn pop_nth(&self, n: usize) -> BTreeMap<Self, Ratio<Num>> {
        let mut v = BTreeMap::new();
        let mut s = *self;
        let start = BOARD_SIZE - s.limit as usize;
        let pops = s.board[n];
        s.board[n] = 0;

        let mut seed_target = Vec::with_capacity(BOARD_SIZE);
        for i in start..BOARD_SIZE {
            if s.board[i] != 0 {
                seed_target.push(Some(i));
            }
        }

        if seed_target.is_empty() {
            seed_target.push(None);
        }

        let one = Ratio::one();

        for st in seed_target {
            let mut s = s;
            if let Some(st) = st {
                s.board[st] += 1;
            }

            let mut pops = pops;
            for j in start..BOARD_SIZE {
                if s.board[j] == 0 && pops > 1 {
                    s.board[j] = 1;
                    pops -= 1;
                } else {
                    break;
                }
            }

            s.sort();
            *v.entry(s).or_insert_with(|| Ratio::zero()) += &one;
        }

        let total_weight = v.values().fold(Ratio::zero(), |acc, val| acc + val);
        for val in v.values_mut() {
            *val /= &total_weight;
        }

        v
    }

    pub fn new(v: &Vec<u8>, limit: u8, optimal_pop: bool) -> Self {
        let mut s = GreyboughState {
            board: [0; BOARD_SIZE],
            limit,
            optimal_pop,
        };
        s.board[..v.len()].copy_from_slice(&v[..]);
        s.sort();
        s
    }

    fn sort(&mut self) {
        self.board.sort_unstable();
    }

    /// board.len <= 25; board.sum() <= 35; board_limit <= 25
    #[cfg(test)]
    pub fn solve_without_cache(
        board: &Vec<u8>,
        board_limit: usize,
        optimal_pop: bool,
    ) -> Ratio<Num> {
        let mut cache = BTreeMap::new();
        Self::solve_with_parsed(board, board_limit, optimal_pop, &mut cache)
    }

    #[cfg(test)]
    pub fn solve_with_parsed(
        board: &Vec<u8>,
        board_limit: usize,
        optimal_pop: bool,
        cache: &mut BTreeMap<GreyboughState, Ratio<Num>>,
    ) -> Ratio<Num> {
        if board_limit > BOARD_SIZE {
            panic!("Board limit too large");
        }
        let sum: u16 = board.iter().map(|&x| x as u16).sum();
        let n1 = -Ratio::one();
        if board.len() > 25 || sum > 35 {
            return n1;
        }

        if sum == 0 {
            return n1;
        }

        let board_limit = std::cmp::min(board_limit as u8, sum as u8);
        let board_limit = std::cmp::min(board_limit, 25);

        let state = GreyboughState::new(board, board_limit, optimal_pop);

        match state.solve(cache) {
            Some(result) => result,
            None => n1,
        }
    }
}
