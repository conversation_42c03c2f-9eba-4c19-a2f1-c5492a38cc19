use num_bigint::BigInt;
use num_rational::Ratio;
use num_traits::{One, Zero};
use std::{collections::BTreeMap, str::FromStr};
type Num = BigInt;

use crate::state_calc::{SolvableStateTransfer, StateTransfer};

const BOARD_SIZE: usize = 64;

#[derive(PartialEq, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialOrd, Ord)]
pub struct DenathriusState {
    targets: [u8; BOARD_SIZE],
    hero: u8,
    remaining: u8,
}

impl StateTransfer<Num> for DenathriusState {
    fn transfer(&self) -> BTreeMap<Self, Ratio<Num>> {
        let mut v = BTreeMap::new();
        if self.remaining == 0 {
            return v;
        }
        let w =
            self.targets.iter().filter(|&&x| x != 0).count() + if self.hero > 0 { 1 } else { 0 };
        let weight = Ratio::one() / Ratio::from_integer(Num::from(w));
        for i in 0..BOARD_SIZE {
            if self.targets[i] == 0 {
                continue;
            }
            let mut s = *self;
            s.targets[i] -= 1;
            s.remaining -= 1;
            s.targets.sort_unstable();
            let entry = v.entry(s).or_default();
            *entry += &weight;
        }

        if self.hero > 0 {
            let mut s = *self;
            s.hero -= 1;
            s.remaining -= 1;
            let entry = v.entry(s).or_default();
            *entry += &weight;
        }

        v
    }
}

impl SolvableStateTransfer<Num> for DenathriusState {
    fn end_evaluation(&self) -> Ratio<Num> {
        if let Some(v) = self.is_early_break() {
            return v;
        }
        Ratio::from_integer(Num::from(self.hero))
    }

    fn stepwise_diff(&self) -> Ratio<Num> {
        Ratio::zero()
    }
}

impl FromStr for DenathriusState {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        let sp = s
            .split(",")
            .filter_map(|s| s.trim().parse::<u8>().ok())
            .collect::<Vec<_>>();
        let remaining = sp[0];
        let hero = sp[1];
        let mut targets = sp[2..].to_vec();
        if targets.len() > BOARD_SIZE {
            return Err(format!("Too many targets: {}", targets.len()));
        }
        targets.resize(BOARD_SIZE, 0);
        targets.sort_unstable();
        let targets = targets
            .try_into()
            .map_err(|_| "Failed to convert targets to array")?;
        Ok(DenathriusState {
            targets,
            hero,
            remaining,
        })
    }
}
