use itertools::Itertools;
use num_bigint::BigInt;
use num_rational::BigRational;
use num_traits::ToPrimitive;
use std::{
    collections::{BTreeMap, HashMap, VecDeque},
    fmt::{Debug, Display},
};

use crate::state_calc::{SolvableStateTransfer, StateTransfer};

const BOARD_SIZE: usize = 64;

#[derive(PartialEq, Eq, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialOrd, Ord)]
pub struct DenathriusState {
    targets: [u8; 7],
    hero: u8,
    remaining: u8,
}

impl StateTransfer<BigInt> for DenathriusState {
    fn transfer(&self) -> BTreeMap<Self, BigRational> {
        let mut v = BTreeMap::new();
        if self.remaining == 0 {
            return v;
        }
        let w = self.targets.iter().filter(|&&x| x != 0).count() + self.hero as usize;
        let weight =
            BigRational::from_integer(BigInt::from(1)) / BigRational::from_integer(BigInt::from(w));
        for i in 0..self.targets.len() {
            if self.targets[i] == 0 {
                continue;
            }
            let mut s = *self;
            s.targets[i] -= 1;
            s.remaining -= 1;
            s.targets.sort_unstable();
            let entry = v.entry(s).or_default();
            *entry += &weight;
        }

        if self.hero > 0 {
            let mut s = *self;
            s.hero -= 1;
            s.remaining -= 1;
            let entry = v.entry(s).or_default();
            *entry += &weight;
        }

        v
    }
}

impl SolvableStateTransfer<BigInt> for DenathriusState {
    fn end_evaluation(&self) -> BigRational {
        BigRational::from_integer(BigInt::from(self.hero))
    }

    fn stepwise_diff(&self) -> BigRational {
        BigRational::from_integer(BigInt::from(0))
    }
}

impl DenathriusState {
    pub fn solve() -> BigRational {
        let initial_state = DenathriusState {
            targets: [6, 6, 6, 6, 6, 6, 6],
            hero: 1,
            remaining: 40,
        };

        let mut cache = BTreeMap::new();
        match initial_state.solve(&mut cache) {
            Some(result) => result,
            None => panic!(),
        }
    }
}
